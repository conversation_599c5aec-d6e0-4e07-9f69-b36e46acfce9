using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

public class Basket
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    // SOAP servisinden gelen GuidSepetID ile eşleştirme için
    [StringLength(100)] public string ExternalId { get; set; } = string.Empty;

    // Hangi şirkete ait olduğu
    public Guid CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; } = null!;

    // <PERSON><PERSON><PERSON>ıyor, ExternalId kullanılıyor
    [StringLength(100)]
    public string? GuidBasketId { get; set; }

    public DateTime BasketDate { get; set; }

    public int CustomerId { get; set; }

    [StringLength(200)]
    public string CustomerName { get; set; } = string.Empty;

    [StringLength(200)]
    public string CustomerEmail { get; set; } = string.Empty;

    public int ProductCount { get; set; } = 0;

    [Column(TypeName = "decimal(10,2)")]
    public decimal TotalAmount { get; set; } = 0.00m;

    [Column(TypeName = "decimal(10,2)")]
    public decimal TotalTax { get; set; } = 0.00m;

    [Column(TypeName = "decimal(10,2)")]
    public decimal ShippingCost { get; set; } = 0.00m;

    [StringLength(10)]
    public string Currency { get; set; } = "TRY";

    public bool IsActive { get; set; } = true;

    public bool IsAbandoned { get; set; } = false;

    public DateTime? LastUpdateDate { get; set; }

    // Senkronizasyon bilgileri
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastSyncDate { get; set; }

    // Navigation properties
    public virtual ICollection<BasketItem> BasketItems { get; set; } = new List<BasketItem>();

    // Helper properties
    [NotMapped]
    public string StatusText => IsActive ? "Aktif" : "Pasif";

    [NotMapped]
    public string StatusBadgeClass => IsActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800";

    [NotMapped]
    public string AbandonedStatusText => IsAbandoned ? "Terk Edilmiş" : "Aktif";

    [NotMapped]
    public string AbandonedStatusBadgeClass => IsAbandoned ? "bg-yellow-100 text-yellow-800" : "bg-blue-100 text-blue-800";

    [NotMapped]
    public string FormattedBasketDate => BasketDate.ToString("dd.MM.yyyy HH:mm");

    [NotMapped]
    public string FormattedLastUpdateDate => LastUpdateDate?.ToString("dd.MM.yyyy HH:mm") ?? "Hiç güncellenmemiş";

    [NotMapped]
    public string FormattedTotalAmount => Currency.ToUpper() switch
    {
        "TRY" => $"{TotalAmount:N2} ₺",
        "USD" => $"${TotalAmount:N2}",
        "EUR" => $"€{TotalAmount:N2}",
        "GBP" => $"£{TotalAmount:N2}",
        _ => $"{TotalAmount:N2} {Currency}"
    };

    // Üyelik durumu helper properties
    [NotMapped]
    public bool HasMembership => CustomerId > 0 && !string.IsNullOrEmpty(CustomerName) && !string.IsNullOrEmpty(CustomerEmail);

    [NotMapped]
    public string MembershipStatusText => HasMembership ? "Üyelikli" : "Üyeliksiz";

    [NotMapped]
    public string MembershipStatusBadgeClass => HasMembership ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800";

    [NotMapped]
    public string DisplayCustomerName => HasMembership ? CustomerName : "Üyeliksiz Sepet";

    [NotMapped]
    public string DisplayCustomerEmail => HasMembership ? CustomerEmail : "Üye bilgisi yok";

    [NotMapped]
    public string ProfileIconClass => HasMembership ? "text-green-500" : "text-gray-400";
}

public class BasketItem
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    public Guid BasketId { get; set; }

    [ForeignKey("BasketId")]
    public virtual Basket Basket { get; set; } = null!;

    // SOAP servisinden gelen ID
    public int ExternalId { get; set; }

    [StringLength(100)]
    public string? GuidBasketItemId { get; set; }

    [StringLength(100)]
    public string ProductCode { get; set; } = string.Empty;

    [StringLength(200)]
    public string ProductName { get; set; } = string.Empty;

    [StringLength(500)]
    public string? ProductImage { get; set; }

    public double Quantity { get; set; } = 1;

    [Column(TypeName = "decimal(10,2)")]
    public decimal UnitPrice { get; set; } = 0.00m;

    [Column(TypeName = "decimal(10,2)")]
    public decimal TotalPrice { get; set; } = 0.00m;

    public double TaxRate { get; set; } = 0;

    [Column(TypeName = "decimal(10,2)")]
    public decimal TaxAmount { get; set; } = 0.00m;

    [Column(TypeName = "decimal(10,2)")]
    public decimal ShippingCost { get; set; } = 0.00m;

    public bool FreeShipping { get; set; } = false;

    [StringLength(10)]
    public string Currency { get; set; } = "TRY";

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    // Helper properties
    [NotMapped]
    public string FormattedUnitPrice => Currency.ToUpper() switch
    {
        "TRY" => $"{UnitPrice:N2} ₺",
        "USD" => $"${UnitPrice:N2}",
        "EUR" => $"€{UnitPrice:N2}",
        "GBP" => $"£{UnitPrice:N2}",
        _ => $"{UnitPrice:N2} {Currency}"
    };

    [NotMapped]
    public string FormattedTotalPrice => Currency.ToUpper() switch
    {
        "TRY" => $"{TotalPrice:N2} ₺",
        "USD" => $"${TotalPrice:N2}",
        "EUR" => $"€{TotalPrice:N2}",
        "GBP" => $"£{TotalPrice:N2}",
        _ => $"{TotalPrice:N2} {Currency}"
    };

    [NotMapped]
    public string FormattedQuantity => Quantity.ToString("N2");
}
