using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

public class Company
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(200)]
    public string? Address { get; set; }

    [StringLength(50)]
    public string? Phone { get; set; }

    [StringLength(100)]
    public string? Email { get; set; }

    [StringLength(200)]
    public string? Website { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Billing Information
    [StringLength(20)]
    public string? BillingType { get; set; } = "Corporate";

    [StringLength(100)]
    public string? TaxOffice { get; set; }

    [StringLength(20)]
    public string? TaxNumber { get; set; }

    [StringLength(11)]
    public string? IdentityNumber { get; set; }

    [StringLength(500)]
    public string? BillingAddress { get; set; }

    [StringLength(100)]
    public string? CompanyName { get; set; }

    [StringLength(100)]
    public string? FullName { get; set; }
    public DateTime? BillingUpdatedAt { get; set; }

    // Company Credit Balance
    [Column(TypeName = "decimal(10,2)")]
    public decimal CreditBalance { get; set; } = 0.00m;

    // Navigation property for users belonging to this company
    public virtual ICollection<ApplicationUser> Users { get; set; } = new List<ApplicationUser>();

    // Navigation property for company modules
    public virtual ICollection<CompanyModule> CompanyModules { get; set; } = new List<CompanyModule>();
}
