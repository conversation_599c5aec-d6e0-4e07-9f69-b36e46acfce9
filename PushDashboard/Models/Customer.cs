using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

public class Customer
{
    public int Id { get; set; }

    // SOAP servisinden gelen ID
    public int ExternalId { get; set; }

    // Hangi şirkete ait olduğu
    public Guid CompanyId { get; set; }

    public virtual Company Company { get; set; } = null!;

    public string FirstName { get; set; } = string.Empty;

    public string LastName { get; set; } = string.Empty;

    public string Email { get; set; } = string.Empty;

    public string? Phone { get; set; }

    public string? MobilePhone { get; set; }

    public bool IsActive { get; set; } = true;

    public bool EmailPermission { get; set; } = false;

    public bool SmsPermission { get; set; } = false;

    public DateTime? BirthDate { get; set; }

    public int? GenderId { get; set; }

    public string? City { get; set; }

    public string? District { get; set; }

    public string? CustomerCode { get; set; }

    public string? MembershipType { get; set; }

    public DateTime MembershipDate { get; set; }

    public DateTime? LastLoginDate { get; set; }

    public string? LastLoginIp { get; set; }

    public DateTime LastUpdateDate { get; set; }

    public int PointBalance { get; set; } = 0;

    public decimal CreditLimit { get; set; } = 0.00m;

    public bool KvkkApproval { get; set; } = false;

    public bool MembershipAgreementApproval { get; set; } = false;

    public bool CashOnDeliveryBlocked { get; set; } = false;

    public string? Profession { get; set; }

    public string? EducationLevel { get; set; }

    // Senkronizasyon bilgileri
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastSyncDate { get; set; }

    // Helper property
    [NotMapped]
    public string FullName => $"{FirstName} {LastName}".Trim();

    [NotMapped]
    public string StatusText => IsActive ? "Aktif" : "Pasif";

    [NotMapped]
    public string StatusBadgeClass => IsActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800";
}
