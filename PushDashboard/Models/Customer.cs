using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

public class Customer
{
    [Key]
    public int Id { get; set; }

    // SOAP servisinden gelen ID
    public int ExternalId { get; set; }

    // Hangi şirkete ait olduğu
    public Guid CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; } = null!;

    [Required]
    [StringLength(100)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string Email { get; set; } = string.Empty;

    [StringLength(20)]
    public string? Phone { get; set; }

    [StringLength(20)]
    public string? MobilePhone { get; set; }

    public bool IsActive { get; set; } = true;

    public bool EmailPermission { get; set; } = false;

    public bool SmsPermission { get; set; } = false;

    public DateTime? BirthDate { get; set; }

    public int? GenderId { get; set; }

    [StringLength(100)]
    public string? City { get; set; }

    [StringLength(100)]
    public string? District { get; set; }

    [StringLength(100)]
    public string? CustomerCode { get; set; }

    [StringLength(100)]
    public string? MembershipType { get; set; }

    public DateTime MembershipDate { get; set; }

    public DateTime? LastLoginDate { get; set; }

    [StringLength(50)]
    public string? LastLoginIp { get; set; }

    public DateTime LastUpdateDate { get; set; }

    public int PointBalance { get; set; } = 0;

    [Column(TypeName = "decimal(10,2)")]
    public decimal CreditLimit { get; set; } = 0.00m;

    public bool KvkkApproval { get; set; } = false;

    public bool MembershipAgreementApproval { get; set; } = false;

    public bool CashOnDeliveryBlocked { get; set; } = false;

    [StringLength(100)]
    public string? Profession { get; set; }

    [StringLength(100)]
    public string? EducationLevel { get; set; }

    // Senkronizasyon bilgileri
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? LastSyncDate { get; set; }

    // Helper property
    [NotMapped]
    public string FullName => $"{FirstName} {LastName}".Trim();

    [NotMapped]
    public string StatusText => IsActive ? "Aktif" : "Pasif";

    [NotMapped]
    public string StatusBadgeClass => IsActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800";
}

public class CustomerImportJob
{
    [Key]
    public int Id { get; set; }

    public int CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; } = null!;

    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;

    [Required]
    [StringLength(500)]
    public string FilePath { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Status { get; set; } = "Bekliyor"; // Bekliyor, İşleniyor, Tamamlandı, Hata

    public int TotalRows { get; set; } = 0;

    public int ProcessedRows { get; set; } = 0;

    public int SuccessCount { get; set; } = 0;

    public int ErrorCount { get; set; } = 0;

    public string? ErrorDetails { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime? StartedAt { get; set; }

    public DateTime? CompletedAt { get; set; }

    [Required]
    [StringLength(450)]
    public string CreatedByUserId { get; set; } = string.Empty;

    // Helper properties
    [NotMapped]
    public int ProgressPercentage => TotalRows > 0 ? (int)((double)ProcessedRows / TotalRows * 100) : 0;

    [NotMapped]
    public string StatusBadgeClass => Status switch
    {
        "Bekliyor" => "bg-yellow-100 text-yellow-800",
        "İşleniyor" => "bg-blue-100 text-blue-800",
        "Tamamlandı" => "bg-green-100 text-green-800",
        "Hata" => "bg-red-100 text-red-800",
        _ => "bg-gray-100 text-gray-800"
    };
}

public class CustomerSyncLog
{
    [Key]
    public int Id { get; set; }

    public int CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; } = null!;

    public DateTime SyncStartTime { get; set; }

    public DateTime? SyncEndTime { get; set; }

    public int TotalRecordsProcessed { get; set; } = 0;

    public int NewRecordsAdded { get; set; } = 0;

    public int RecordsUpdated { get; set; } = 0;

    public bool IsSuccessful { get; set; } = false;

    [StringLength(1000)]
    public string? ErrorMessage { get; set; }

    [StringLength(500)]
    public string? Notes { get; set; }

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
