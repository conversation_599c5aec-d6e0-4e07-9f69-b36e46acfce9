using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PushDashboard.Models;

public class EmailTemplate
{
    [Key]
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string Category { get; set; } = string.Empty; // Müşteri, Sepet, Sipariş

    [Required]
    [StringLength(200)]
    public string Description { get; set; } = string.Empty;

    [Required]
    public string DefaultContent { get; set; } = string.Empty; // HTML content

    [StringLength(100)]
    public string? DefaultSubject { get; set; }

    public string? Variables { get; set; } // JSON array of available variables

    public int? ModuleId { get; set; } // Hangi modüle bağlı (null = herkese açık)

    public bool IsActive { get; set; } = true;

    public int SortOrder { get; set; } = 0;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual ICollection<CompanyEmailTemplate> CompanyTemplates { get; set; } = new List<CompanyEmailTemplate>();
}

public class CompanyEmailTemplate
{
    [Key]
    public int Id { get; set; }

    [Required]
    public Guid CompanyId { get; set; }

    [ForeignKey("CompanyId")]
    public virtual Company Company { get; set; } = null!;

    public int EmailTemplateId { get; set; }

    // No foreign key constraint for custom templates (negative IDs)
    public virtual EmailTemplate? EmailTemplate { get; set; }

    [Required]
    public string CustomContent { get; set; } = string.Empty; // Şirketin özelleştirdiği içerik

    [StringLength(200)]
    public string? CustomSubject { get; set; }

    public bool IsEnabled { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    public string? LastModifiedBy { get; set; } // User ID

    [ForeignKey("LastModifiedBy")]
    public virtual ApplicationUser? LastModifiedByUser { get; set; }
}
