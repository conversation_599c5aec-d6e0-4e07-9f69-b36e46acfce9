namespace PushDashboard.Services.Modules.FirstOrder;

public interface IFirstOrderModuleService
{
    /// <summary>
    /// İlk alışveriş yapan müşteri için bildirim gönderir
    /// </summary>
    Task<(bool Success, string Message, int NotificationsSent, decimal TotalCost)> SendFirstOrderNotificationAsync(
        Guid companyId, int customerId, string userId);

    /// <summary>
    /// Müşterinin daha önce sipariş verip vermediğini kontrol eder
    /// </summary>
    Task<bool> IsFirstOrderAsync(Guid companyId, int customerId);
}
