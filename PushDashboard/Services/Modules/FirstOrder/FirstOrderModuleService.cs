using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.Services.Integrations;
using PushDashboard.Services.Integrations.Common;
using PushDashboard.Services.Integrations.Common.Models;
using System.Text.Json;
using PushDashboard.Services.Notifications;
using PushDashboard.Services;

namespace PushDashboard.Services.Modules.FirstOrder;

public class FirstOrderModuleService : IFirstOrderModuleService
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<FirstOrderModuleService> _logger;
    private readonly INotificationChannelFactory _channelFactory;
    private readonly EcommerceGiftVoucherFactory _giftVoucherFactory;
    private readonly IEcommerceService _ecommerceService;
    private readonly IModuleUsageService _moduleUsageService;

    public FirstOrderModuleService(
        ApplicationDbContext context,
        ILogger<FirstOrderModuleService> logger,
        INotificationChannelFactory channelFactory,
        EcommerceGiftVoucherFactory giftVoucherFactory,
        IEcommerceService ecommerceService,
        IModuleUsageService moduleUsageService)
    {
        _context = context;
        _logger = logger;
        _channelFactory = channelFactory;
        _giftVoucherFactory = giftVoucherFactory;
        _ecommerceService = ecommerceService;
        _moduleUsageService = moduleUsageService;
    }

    public async Task<(bool Success, string Message, int NotificationsSent, decimal TotalCost)> SendFirstOrderNotificationAsync(
        Guid companyId, int customerId, string userId)
    {
        try
        {
            _logger.LogInformation("Processing first order notification for customer {CustomerId} in company {CompanyId}",
                customerId, companyId);

            // İlk alışveriş modülünü kontrol et
            var firstOrderModule = await _context.Modules
                .FirstOrDefaultAsync(m => m.Name == "İlk Alışveriş Mesajı" && m.IsActive);

            if (firstOrderModule == null)
            {
                return (false, "İlk alışveriş mesajı modülü bulunamadı.", 0, 0);
            }

            // Şirketin bu modüle sahip olup olmadığını kontrol et
            var companyModule = await _context.CompanyModules
                .FirstOrDefaultAsync(cm => cm.CompanyId == companyId &&
                                         cm.ModuleId == firstOrderModule.Id &&
                                         cm.IsActive);

            if (companyModule == null)
            {
                return (false, "Şirket bu modüle sahip değil.", 0, 0);
            }

            // Modül ayarlarını al
            var moduleSettings = await GetModuleSettingsAsync(companyId, firstOrderModule.Id);
            if (moduleSettings == null || !IsModuleEnabled(moduleSettings))
            {
                return (false, "Modül etkin değil.", 0, 0);
            }

            // Müşteri bilgilerini al
            var customer = await _context.Customers
                .FirstOrDefaultAsync(c => c.Id == customerId && c.CompanyId == companyId);

            if (customer == null)
            {
                return (false, "Müşteri bulunamadı.", 0, 0);
            }

            // Şirket bilgilerini al
            var company = await _context.Companies
                .FirstOrDefaultAsync(c => c.Id == companyId);

            if (company == null)
            {
                return (false, "Şirket bulunamadı.", 0, 0);
            }

            // İlk sipariş kontrolü
            var isFirstOrder = await IsFirstOrderAsync(companyId, customerId);
            if (!isFirstOrder)
            {
                return (false, "Bu müşterinin daha önce siparişi var.", 0, 0);
            }

            // Etkin iletişim kanallarını al
            var enabledChannels = await GetEnabledChannelsAsync(companyId, moduleSettings);
            if (!enabledChannels.Any())
            {
                return (false, "Etkin iletişim kanalı bulunamadı.", 0, 0);
            }

            // Toplam maliyet hesaplama
            decimal totalEstimatedCost = 0;
            var channelCosts = new Dictionary<string, decimal>();

            foreach (var channel in enabledChannels)
            {
                var cost = GetChannelCost(channel.ChannelType);
                channelCosts[channel.ChannelType] = cost;
                totalEstimatedCost += cost;
            }

            // Kredi kontrolü
            if (company.CreditBalance < totalEstimatedCost)
            {
                return (false, $"Yetersiz kredi. Gerekli: {totalEstimatedCost:C}, Mevcut: {company.CreditBalance:C}", 0, 0);
            }

            _logger.LogInformation("Sending first order notifications to {Count} channels for customer {CustomerId}",
                enabledChannels.Count, customerId);

            int notificationsSent = 0;
            decimal totalCost = 0;

            // Hediye çeki oluştur (eğer etkinse ve Ticimax entegrasyonu varsa)
            string? giftVoucherCode = null;
            string? giftVoucherAmount = null;

            if (await ShouldCreateGiftVoucherAsync(companyId, moduleSettings))
            {
                var giftVoucherResult = await CreateGiftVoucherAsync(companyId, customer, moduleSettings);
                if (giftVoucherResult.Success)
                {
                    giftVoucherCode = giftVoucherResult.VoucherCode;
                    giftVoucherAmount = giftVoucherResult.Amount;
                }
            }

            var variables = CreateFirstOrderVariables(customer, company, giftVoucherCode, giftVoucherAmount);

            // Her etkin kanal için bildirim gönder
            foreach (var channel in enabledChannels)
            {
                try
                {
                    var cost = channelCosts[channel.ChannelType];

                    // Maliyet takibi ile bildirim gönder
                    bool sent = false;
                    if (channel is EmailNotificationChannelService emailChannel)
                    {
                        var stringVariables = variables.ToDictionary(kv => kv.Key, kv => kv.Value?.ToString() ?? "");
                        sent = await emailChannel.SendNotificationWithCostTrackingAsync(
                            companyId,
                            customer,
                            "İlk Alışveriş Tebriği",
                            stringVariables,
                            userId,
                            firstOrderModule.Id,
                            $"first_order_{customer.Id}_{DateTime.Now:yyyyMMdd}"
                        );
                    }
                    else
                    {
                        // Diğer kanallar için normal gönderim (gelecekte maliyet takibi eklenecek)
                        var stringVariables = variables.ToDictionary(kv => kv.Key, kv => kv.Value?.ToString() ?? "");
                        sent = await channel.SendNotificationAsync(companyId, customer, "İlk Alışveriş Tebriği", stringVariables);
                    }

                    if (sent)
                    {
                        notificationsSent++;
                        totalCost += cost;
                        _logger.LogInformation("First order notification sent via {ChannelType} to customer {CustomerId}",
                            channel.ChannelType, customerId);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to send first order notification via {ChannelType} to customer {CustomerId}",
                            channel.ChannelType, customerId);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error sending first order notification via {ChannelType} to customer {CustomerId}",
                        channel.ChannelType, customerId);
                }
            }

            if (notificationsSent > 0)
            {
                return (true, $"{notificationsSent} kanal üzerinden ilk alışveriş mesajı gönderildi.", notificationsSent, totalCost);
            }

            return (false, "Hiçbir kanal üzerinden mesaj gönderilemedi.", 0, 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing first order notification for customer {CustomerId} in company {CompanyId}",
                customerId, companyId);
            return (false, "İlk alışveriş mesajı gönderilirken hata oluştu: " + ex.Message, 0, 0);
        }
    }

    public async Task<bool> IsFirstOrderAsync(Guid companyId, int customerId)
    {
        try
        {
            // Müşteri bilgilerini al
            var customer = await _context.Customers
                .FirstOrDefaultAsync(c => c.Id == customerId && c.CompanyId == companyId);

            if (customer == null || customer.ExternalId <= 0)
            {
                _logger.LogWarning("Customer {CustomerId} not found or has invalid ExternalId", customerId);
                return false;
            }

            // Şirketin Ticimax entegrasyonunu al
            var ticimaxIntegration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "Ticimax" &&
                                         ci.IsActive && ci.IsConfigured);

            if (ticimaxIntegration == null)
            {
                _logger.LogWarning("No active Ticimax integration found for company {CompanyId}", companyId);
                return false;
            }

            var settings = ticimaxIntegration.Settings;
            var baseUrl = settings.GetValueOrDefault("apiUrl", "https://perlucia.ticimaxtest.com").ToString();
            var apiKey = settings.GetValueOrDefault("apiKey", "").ToString();

            if (string.IsNullOrEmpty(apiKey))
            {
                _logger.LogWarning("Ticimax API key not found for company {CompanyId}", companyId);
                return false;
            }

            // Filtre oluştur - sadece bu müşterinin siparişleri
            var filter = new EcommerceOrderFilter
            {
                CustomerId = customer.ExternalId.ToString(),
                PaymentCompleted = true
            };

            // Sayfalama - sadece 2 kayıt yeterli (varsa ilk sipariş değil)
            var pagination = new EcommercePagination
            {
                PageNumber = 1,
                PageSize = 2,
                SortField = "OrderDate",
                SortDirection = "ASC"
            };

            // E-commerce service ile müşterinin geçmiş siparişlerini sorgula
            var orders = await _ecommerceService.GetOrdersAsync(companyId, filter, pagination);

            // Eğer hiç sipariş yoksa, bu ilk sipariş
            var isFirstOrder = orders != null &&orders.Length == 1;

            _logger.LogInformation("Customer {CustomerId} (ExternalId: {ExternalId}) first order check: {IsFirstOrder}. Found {OrderCount} previous orders.",
                customerId, customer.ExternalId, isFirstOrder, orders?.Length ?? 0);

            return isFirstOrder;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if customer {CustomerId} has first order", customerId);
            return false; // Hata durumunda güvenli tarafta kal
        }
    }

    private async Task<Dictionary<string, object>?> GetModuleSettingsAsync(Guid companyId, int moduleId)
    {
        try
        {
            var companyModule = await _context.CompanyModules
                .Include(cm => cm.Settings)
                .FirstOrDefaultAsync(cm => cm.CompanyId == companyId && cm.ModuleId == moduleId && cm.IsActive);

            if (companyModule?.Settings != null)
            {
                return companyModule.Settings.Settings;
            }

            // Varsayılan ayarları al
            var module = await _context.Modules.FirstOrDefaultAsync(m => m.Id == moduleId);
            if (module?.DefaultSettings != null)
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(module.DefaultSettings);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting module settings for company {CompanyId} and module {ModuleId}", companyId, moduleId);
            return null;
        }
    }

    private bool IsModuleEnabled(Dictionary<string, object> settings)
    {
        if (settings.TryGetValue("enabled", out var enabledValue))
        {
            if (enabledValue is bool enabled)
                return enabled;
            if (enabledValue is JsonElement element && element.ValueKind == JsonValueKind.True)
                return true;
        }
        return false;
    }

    private async Task<bool> ShouldCreateGiftVoucherAsync(Guid companyId, Dictionary<string, object> settings)
    {
        try
        {
            // Hediye çeki etkin mi kontrol et
            if (!settings.TryGetValue("giftVoucherEnabled", out var enabledValue))
                return false;

            bool isEnabled = false;
            if (enabledValue is bool enabled)
                isEnabled = enabled;
            else if (enabledValue is JsonElement element && element.ValueKind == JsonValueKind.True)
                isEnabled = true;

            if (!isEnabled)
                return false;

            // Herhangi bir e-ticaret entegrasyonu var mı ve hediye çeki destekliyor mu kontrol et
            return await _giftVoucherFactory.HasActiveEcommerceIntegrationAsync(companyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if gift voucher should be created for company {CompanyId}", companyId);
            return false;
        }
    }

    private async Task<(bool Success, string? VoucherCode, string? Amount)> CreateGiftVoucherAsync(
        Guid companyId, Customer customer, Dictionary<string, object> settings)
    {
        try
        {
            // Hediye çeki ayarlarını al
            var giftVoucherSettings = ExtractGiftVoucherSettings(settings);

            // Factory üzerinden hediye çeki oluştur
            var result = await _giftVoucherFactory.CreateGiftVoucherAsync(companyId, customer, giftVoucherSettings);

            if (result.Success)
            {
                _logger.LogInformation("Gift voucher created for first order customer {CustomerId}: {VoucherCode}",
                    customer.Id, result.VoucherCode);
                return (true, result.VoucherCode, result.Amount);
            }
            else
            {
                _logger.LogWarning("Failed to create gift voucher for first order customer {CustomerId}: {Message}",
                    customer.Id, result.ErrorMessage);
                return (false, null, null);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating gift voucher for first order customer {CustomerId}", customer.Id);
            return (false, null, null);
        }
    }

    private GiftVoucherSettings ExtractGiftVoucherSettings(Dictionary<string, object> settings)
    {
        var giftVoucherSettings = new GiftVoucherSettings
        {
            Description = "İlk Alışveriş Hediye Çeki" // İlk alışveriş için özel açıklama
        };

        if (settings.TryGetValue("giftVoucherAmount", out var amountValue))
        {
            if (amountValue is decimal amount)
                giftVoucherSettings.Amount = (double)amount;
            else if (amountValue is JsonElement amountElement && amountElement.TryGetDecimal(out var amountDecimal))
                giftVoucherSettings.Amount = (double)amountDecimal;
        }

        if (settings.TryGetValue("giftVoucherDiscountType", out var discountTypeValue))
        {
            if (discountTypeValue is int discountType)
                giftVoucherSettings.DiscountType = discountType;
            else if (discountTypeValue is JsonElement discountElement && discountElement.TryGetInt32(out var discountInt))
                giftVoucherSettings.DiscountType = discountInt;
        }

        if (settings.TryGetValue("giftVoucherValidityDays", out var validityValue))
        {
            if (validityValue is int validity)
                giftVoucherSettings.ValidityDays = validity;
            else if (validityValue is JsonElement validityElement && validityElement.TryGetInt32(out var validityInt))
                giftVoucherSettings.ValidityDays = validityInt;
        }

        return giftVoucherSettings;
    }

    private Dictionary<string, object> CreateFirstOrderVariables(Customer customer, Company company, string? giftVoucherCode, string? giftVoucherAmount)
    {
        var variables = new Dictionary<string, object>
        {
            ["customer_name"] = $"{customer.FirstName} {customer.LastName}".Trim(),
            ["customer_first_name"] = customer.FirstName ?? "",
            ["customer_last_name"] = customer.LastName ?? "",
            ["customer_email"] = customer.Email ?? "",
            ["company_name"] = company.Name ?? "",
            ["current_date"] = DateTime.Now.ToString("dd.MM.yyyy"),
            ["current_time"] = DateTime.Now.ToString("HH:mm")
        };

        // Hediye çeki bilgileri varsa ekle
        if (!string.IsNullOrEmpty(giftVoucherCode))
        {
            variables["gift_voucher_code"] = giftVoucherCode;
            variables["gift_voucher_amount"] = giftVoucherAmount ?? "";
            variables["has_gift_voucher"] = true;
        }
        else
        {
            variables["gift_voucher_code"] = "";
            variables["gift_voucher_amount"] = "";
            variables["has_gift_voucher"] = false;
        }

        return variables;
    }

    private async Task<List<INotificationChannelService>> GetEnabledChannelsAsync(Guid companyId, Dictionary<string, object> moduleSettings)
    {
        var enabledChannels = new List<INotificationChannelService>();

        try
        {
            // Email kanalı kontrol et
            if (IsChannelEnabled(moduleSettings, "email"))
            {
                var emailChannel = await _channelFactory.GetChannelAsync(companyId, "email");
                if (emailChannel != null)
                {
                    enabledChannels.Add(emailChannel);
                }
            }

            // SMS kanalı kontrol et (gelecekte eklenecek)
            if (IsChannelEnabled(moduleSettings, "sms"))
            {
                var smsChannel = await _channelFactory.GetChannelAsync(companyId, "sms");
                if (smsChannel != null)
                {
                    enabledChannels.Add(smsChannel);
                }
            }

            // Push notification kanalı kontrol et (gelecekte eklenecek)
            if (IsChannelEnabled(moduleSettings, "push"))
            {
                var pushChannel = await _channelFactory.GetChannelAsync(companyId, "push");
                if (pushChannel != null)
                {
                    enabledChannels.Add(pushChannel);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting enabled channels for company {CompanyId}", companyId);
        }

        return enabledChannels;
    }

    private bool IsChannelEnabled(Dictionary<string, object> settings, string channelType)
    {
        var settingKey = $"{channelType}Enabled";
        if (settings.TryGetValue(settingKey, out var enabledValue))
        {
            if (enabledValue is bool enabled)
                return enabled;
            if (enabledValue is JsonElement element && element.ValueKind == JsonValueKind.True)
                return true;
        }
        return false;
    }

    private decimal GetChannelCost(string channelType)
    {
        return channelType.ToLower() switch
        {
            "email" => 0.10m,
            "sms" => 0.25m,
            "push" => 0.05m,
            _ => 0.00m
        };
    }
}