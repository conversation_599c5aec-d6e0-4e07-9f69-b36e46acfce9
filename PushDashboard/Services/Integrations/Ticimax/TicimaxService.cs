using System.ServiceModel;
using Microsoft.EntityFrameworkCore;
using PushDashboard.CustomServis;
using PushDashboard.SiparisServis;
using PushDashboard.UyeServis;
using PushDashboard.Data;
using PushDashboard.Services.Integrations.Common;
using PushDashboard.Services.Integrations.Common.Models;
using PushDashboard.Services.Integrations.Ticimax.Mappers;

namespace PushDashboard.Services.Integrations.Ticimax;

/// <summary>
/// Ticimax API ile iletişim kuran temel servis
/// Implements global IEcommerceService interface for platform-agnostic operations
/// </summary>
public class TicimaxService : IEcommerceService
{
    private readonly ILogger<TicimaxService> _logger;
    private readonly ApplicationDbContext _context;

    public TicimaxService(ILogger<TicimaxService> logger, ApplicationDbContext context)
    {
        _logger = logger;
        _context = context;
    }

    /// <summary>
    /// Platform identifier for Ticimax
    /// </summary>
    public string PlatformName => "Ticimax";

    #region Client Factory Methods

    /// <summary>
    /// CustomServis SOAP client'ı oluşturur
    /// </summary>
    public CustomServisClient CreateCustomServiceClient(string baseUrl)
    {
        var serviceUrl = $"{baseUrl.TrimEnd('/')}/Servis/CustomServis.svc";
        var binding = new BasicHttpBinding();
        binding.Security.Mode = BasicHttpSecurityMode.Transport;
        var endpoint = new EndpointAddress(serviceUrl);
        return new CustomServisClient(binding, endpoint);
    }

    /// <summary>
    /// SiparisServis SOAP client'ı oluşturur
    /// </summary>
    public SiparisServisClient CreateOrderServiceClient(string baseUrl)
    {
        var serviceUrl = $"{baseUrl.TrimEnd('/')}/Servis/SiparisServis.svc";
        var binding = new BasicHttpBinding();
        binding.Security.Mode = BasicHttpSecurityMode.Transport;
        var endpoint = new EndpointAddress(serviceUrl);
        return new SiparisServisClient(binding, endpoint);
    }

    /// <summary>
    /// UyeServis SOAP client'ı oluşturur
    /// </summary>
    public UyeServisClient CreateMemberServiceClient(string baseUrl)
    {
        var serviceUrl = $"{baseUrl.TrimEnd('/')}/Servis/UyeServis.svc";
        var binding = new BasicHttpBinding();
        binding.Security.Mode = BasicHttpSecurityMode.Transport;
        var endpoint = new EndpointAddress(serviceUrl);
        return new UyeServisClient(binding, endpoint);
    }

    #endregion

    #region Private Helper Methods

    /// <summary>
    /// Company ID'den Ticimax API bilgilerini getirir
    /// </summary>
    private async Task<(string ApiKey, string BaseUrl)?> GetTicimaxApiInfoAsync(Guid companyId)
    {
        try
        {
            var integration = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .FirstOrDefaultAsync(ci => ci.CompanyId == companyId &&
                                         ci.Integration.Name == "Ticimax" &&
                                         ci.IsActive && ci.IsConfigured);

            if (integration?.Settings == null)
            {
                _logger.LogWarning("Ticimax integration not found or not configured for company {CompanyId}", companyId);
                return null;
            }

            var apiKey = integration.Settings.GetValueOrDefault("apiKey")?.ToString();
            var baseUrl = integration.Settings.GetValueOrDefault("apiUrl")?.ToString();

            if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(baseUrl))
            {
                _logger.LogWarning("Ticimax API credentials incomplete for company {CompanyId}", companyId);
                return null;
            }

            return (apiKey, baseUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting Ticimax API info for company {CompanyId}", companyId);
            return null;
        }
    }

    #endregion

    #region IEcommerceService Implementation

    /// <summary>
    /// Gets webhooks using global e-commerce models
    /// </summary>
    public async Task<EcommerceWebhook[]> GetWebhooksAsync(Guid companyId)
    {
        var apiInfo = await GetTicimaxApiInfoAsync(companyId);
        if (apiInfo == null)
            return new EcommerceWebhook[0];

        try
        {
            using var client = CreateCustomServiceClient(apiInfo.Value.BaseUrl);

            var request = new SelectWebhookRequest
            {
                ID = 0, // Tüm webhook'ları getir
                IslemTipi = null // Tüm işlem tiplerini getir
            };

            var response = await client.SelectWebhookAsync(apiInfo.Value.ApiKey, request);

            if (response != null && !response.IsError)
            {
                var ticimaxWebhooks = response.WebhookList ?? new Webhook[0];
                return ticimaxWebhooks.Select(TicimaxMapper.MapToEcommerceWebhook).ToArray();
            }

            _logger.LogWarning("Ticimax webhook query returned error: {Error}", response?.ErrorMessage);
            return new EcommerceWebhook[0];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting webhooks from Ticimax API for company {CompanyId}", companyId);
            return new EcommerceWebhook[0];
        }
    }

    /// <summary>
    /// Saves webhook using global e-commerce models
    /// </summary>
    public async Task<EcommerceWebhookResult> SaveWebhookAsync(Guid companyId, EcommerceWebhookRequest request)
    {
        var apiInfo = await GetTicimaxApiInfoAsync(companyId);
        if (apiInfo == null)
            return new EcommerceWebhookResult
            {
                Success = false,
                Message = "Ticimax entegrasyonu bulunamadı veya yapılandırılmamış",
                ErrorCode = "INTEGRATION_NOT_FOUND"
            };

        try
        {
            using var client = CreateCustomServiceClient(apiInfo.Value.BaseUrl);

            var ticimaxWebhook = TicimaxMapper.MapToTicimaxWebhook(request);
            var ticimaxEventType = TicimaxMapper.MapToTicimaxWebhookType(request.EventType);

            var webhook = new Webhook
            {
                ID = 0, // Yeni webhook için 0
                IslemTipi = ticimaxEventType,
                Url = request.Url,
                KullaniciAdi = request.Username ?? "pushonica",
                Sifre = request.Password ?? "pushonica123!"
            };

            var response = await client.SaveWebhookAsync(apiInfo.Value.ApiKey, webhook);

            if (response != null && !response.IsError)
            {
                _logger.LogInformation("Webhook saved successfully for company {CompanyId}: {EventType} -> {Url}",
                    companyId, request.EventType, request.Url);

                return new EcommerceWebhookResult
                {
                    Success = true,
                    Message = "Webhook başarıyla kaydedildi",
                    WebhookId = response.ID.ToString(),
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["platform"] = PlatformName,
                        ["ticimaxWebhookId"] = response.ID
                    }
                };
            }

            var errorMessage = response?.ErrorMessage ?? "Bilinmeyen hata";
            _logger.LogWarning("Failed to save webhook for company {CompanyId}: {Error}", companyId, errorMessage);

            return new EcommerceWebhookResult
            {
                Success = false,
                Message = errorMessage,
                ErrorCode = "TICIMAX_ERROR"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving webhook for company {CompanyId}", companyId);
            return new EcommerceWebhookResult
            {
                Success = false,
                Message = "Webhook kaydedilirken hata oluştu: " + ex.Message,
                ErrorCode = "EXCEPTION"
            };
        }
    }

    /// <summary>
    /// Deletes webhook using global e-commerce models
    /// </summary>
    public async Task<EcommerceResult> DeleteWebhookAsync(Guid companyId, string webhookId)
    {
        if (!int.TryParse(webhookId, out var ticimaxWebhookId))
        {
            return new EcommerceResult
            {
                Success = false,
                Message = "Invalid webhook ID format",
                ErrorCode = "INVALID_WEBHOOK_ID"
            };
        }

        var apiInfo = await GetTicimaxApiInfoAsync(companyId);
        if (apiInfo == null)
            return new EcommerceResult
            {
                Success = false,
                Message = "Ticimax entegrasyonu bulunamadı veya yapılandırılmamış",
                ErrorCode = "INTEGRATION_NOT_FOUND"
            };

        try
        {
            using var client = CreateCustomServiceClient(apiInfo.Value.BaseUrl);

            var response = await client.DeleteWebhookAsync(apiInfo.Value.ApiKey, ticimaxWebhookId);

            if (response != null && !response.IsError)
            {
                _logger.LogInformation("Webhook deleted successfully for company {CompanyId}: {WebhookId}", companyId, ticimaxWebhookId);
                return new EcommerceResult
                {
                    Success = true,
                    Message = "Webhook başarıyla silindi",
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["platform"] = PlatformName,
                        ["ticimaxWebhookId"] = ticimaxWebhookId
                    }
                };
            }

            var errorMessage = response?.ErrorMessage ?? "Bilinmeyen hata";
            _logger.LogWarning("Failed to delete webhook for company {CompanyId}: {Error}", companyId, errorMessage);
            return new EcommerceResult
            {
                Success = false,
                Message = errorMessage,
                ErrorCode = "TICIMAX_ERROR"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting webhook for company {CompanyId}", companyId);
            return new EcommerceResult
            {
                Success = false,
                Message = "Webhook silinirken hata oluştu: " + ex.Message,
                ErrorCode = "EXCEPTION"
            };
        }
    }

    /// <summary>
    /// Gets customers using global e-commerce models
    /// </summary>
    public async Task<EcommerceCustomer[]> GetCustomersAsync(Guid companyId, EcommerceCustomerFilter filter, EcommercePagination pagination)
    {
        var apiInfo = await GetTicimaxApiInfoAsync(companyId);
        if (apiInfo == null)
            return new EcommerceCustomer[0];

        try
        {
            var ticimaxFilter = TicimaxMapper.MapToTicimaxCustomerFilter(filter);
            var ticimaxPagination = TicimaxMapper.MapToTicimaxPagination(pagination);

            using var client = CreateMemberServiceClient(apiInfo.Value.BaseUrl);
            var ticimaxCustomers = await client.SelectUyelerAsync(apiInfo.Value.ApiKey, ticimaxFilter, ticimaxPagination);

            return ticimaxCustomers?.Select(TicimaxMapper.MapToEcommerceCustomer).ToArray() ?? new EcommerceCustomer[0];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting customers from Ticimax API for company {CompanyId}", companyId);
            return new EcommerceCustomer[0];
        }
    }

    /// <summary>
    /// Gets a specific customer by external ID
    /// </summary>
    public async Task<EcommerceCustomer?> GetCustomerAsync(Guid companyId, string externalCustomerId)
    {
        if (!int.TryParse(externalCustomerId, out var ticimaxCustomerId))
        {
            return null;
        }

        var filter = new EcommerceCustomerFilter();
        var pagination = new EcommercePagination
        {
            PageNumber = 1,
            PageSize = 100, // Get more records to search through
            SortField = "ID",
            SortDirection = "ASC"
        };

        // Note: Ticimax doesn't have a direct "get customer by ID" method
        // We get customers and filter client-side (not ideal for production)
        var customers = await GetCustomersAsync(companyId, filter, pagination);
        return customers.FirstOrDefault(c => c.ExternalId == externalCustomerId);
    }

    /// <summary>
    /// Gets orders using global e-commerce models
    /// </summary>
    public async Task<EcommerceOrder[]> GetOrdersAsync(Guid companyId, EcommerceOrderFilter filter, EcommercePagination pagination)
    {
        var apiInfo = await GetTicimaxApiInfoAsync(companyId);
        if (apiInfo == null)
            return new EcommerceOrder[0];

        try
        {
            var ticimaxFilter = TicimaxMapper.MapToTicimaxOrderFilter(filter);
            var ticimaxPagination = TicimaxMapper.MapToTicimaxOrderPagination(pagination);

            using var client = CreateOrderServiceClient(apiInfo.Value.BaseUrl);
            var ticimaxOrders = await client.SelectSiparisAsync(apiInfo.Value.ApiKey, ticimaxFilter, ticimaxPagination);

            return ticimaxOrders?.Select(TicimaxMapper.MapToEcommerceOrder).ToArray() ?? new EcommerceOrder[0];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting orders from Ticimax API for company {CompanyId}", companyId);
            return new EcommerceOrder[0];
        }
    }

    /// <summary>
    /// Gets a specific order by external ID
    /// </summary>
    public async Task<EcommerceOrder?> GetOrderAsync(Guid companyId, string externalOrderId)
    {
        if (!int.TryParse(externalOrderId, out var ticimaxOrderId))
        {
            return null;
        }

        var filter = new EcommerceOrderFilter();
        var pagination = new EcommercePagination
        {
            PageNumber = 1,
            PageSize = 100, // Get more records to search through
            SortField = "ID",
            SortDirection = "ASC"
        };

        // Note: Similar to customers, Ticimax doesn't have direct "get order by ID"
        // This is a simplified implementation
        var orders = await GetOrdersAsync(companyId, filter, pagination);
        return orders.FirstOrDefault(o => o.ExternalId == externalOrderId);
    }

    /// <summary>
    /// Gets carts using global e-commerce models
    /// </summary>
    public async Task<EcommerceCart[]> GetCartsAsync(Guid companyId, EcommerceCartFilter filter, EcommercePagination pagination)
    {
        var apiInfo = await GetTicimaxApiInfoAsync(companyId);
        if (apiInfo == null)
            return new EcommerceCart[0];

        try
        {
            using var client = CreateOrderServiceClient(apiInfo.Value.BaseUrl);
            var response = await client.SelectSepetAsync(apiInfo.Value.ApiKey, 0, 0, filter.CreatedAfter, filter.CreatedBefore, pagination.PageNumber, null);

            return response?.Sepetler?.Select(TicimaxMapper.MapToEcommerceCart).ToArray() ?? new EcommerceCart[0];
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting carts from Ticimax API for company {CompanyId}", companyId);
            return new EcommerceCart[0];
        }
    }

    /// <summary>
    /// Creates gift voucher using global e-commerce models
    /// </summary>
    public async Task<EcommerceGiftVoucherResult> CreateGiftVoucherAsync(Guid companyId, EcommerceGiftVoucherRequest request)
    {
        var apiInfo = await GetTicimaxApiInfoAsync(companyId);
        if (apiInfo == null)
            return new EcommerceGiftVoucherResult
            {
                Success = false,
                Message = "Ticimax entegrasyonu bulunamadı",
                ErrorCode = "INTEGRATION_NOT_FOUND"
            };

        try
        {
            var ticimaxRequest = TicimaxMapper.MapToTicimaxGiftVoucherRequest(request);

            using var client = CreateOrderServiceClient(apiInfo.Value.BaseUrl);
            var response = await client.HediyeCekiOlusturAsync(apiInfo.Value.ApiKey, ticimaxRequest);

            return new EcommerceGiftVoucherResult
            {
                Success = !response.IsError,
                Message = response.IsError ? response.ErrorMessage : "Gift voucher created successfully",
                VoucherCode = response.HediyeCekiKodu,
                VoucherId = response.HediyeCekiId.ToString(),
                ErrorCode = response.IsError ? "TICIMAX_ERROR" : null,
                AdditionalInfo = new Dictionary<string, object>
                {
                    ["platform"] = PlatformName,
                    ["ticimaxVoucherId"] = response.HediyeCekiId,
                    ["ticimaxVoucherCode"] = response.HediyeCekiKodu ?? string.Empty
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating gift voucher in Ticimax API for company {CompanyId}", companyId);
            return new EcommerceGiftVoucherResult
            {
                Success = false,
                Message = ex.Message,
                ErrorCode = "EXCEPTION"
            };
        }
    }

    /// <summary>
    /// Tests connection using global e-commerce models
    /// </summary>
    public async Task<EcommerceConnectionResult> TestConnectionAsync(Guid companyId)
    {
        var apiInfo = await GetTicimaxApiInfoAsync(companyId);
        if (apiInfo == null)
            return new EcommerceConnectionResult
            {
                Success = false,
                Message = "Ticimax entegrasyonu bulunamadı veya yapılandırılmamış",
                PlatformName = PlatformName,
                ErrorCode = "INTEGRATION_NOT_FOUND"
            };

        try
        {
            // Basit bir webhook sorgusu ile bağlantıyı test et
            var webhooks = await GetWebhooksAsync(companyId);

            return new EcommerceConnectionResult
            {
                Success = true,
                Message = "Bağlantı başarılı",
                PlatformName = PlatformName,
                PlatformVersion = "SOAP API", // Ticimax uses SOAP
                ConnectionInfo = new Dictionary<string, object>
                {
                    ["platform"] = PlatformName,
                    ["apiType"] = "SOAP",
                    ["companyId"] = companyId,
                    ["testMethod"] = "webhook_query",
                    ["webhookCount"] = webhooks.Length
                }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Connection test failed for Ticimax API for company {CompanyId}", companyId);
            return new EcommerceConnectionResult
            {
                Success = false,
                Message = $"Bağlantı hatası: {ex.Message}",
                PlatformName = PlatformName,
                ErrorCode = "CONNECTION_FAILED"
            };
        }
    }

    /// <summary>
    /// Tests connection using provided apiUrl and apiKey (for form testing)
    /// </summary>
    public async Task<EcommerceConnectionResult> TestConnectionAsync(string apiUrl, string apiKey)
    {
        try
        {
            _logger.LogInformation("Testing Ticimax connection to {ApiUrl}", apiUrl);

            var client = CreateCustomServiceClient(apiUrl);
            var contents = await client.SelectWebhookAsync(apiKey, new SelectWebhookRequest() { ID = 0, IslemTipi = null });


            if (!contents.IsError)
            {
                return new EcommerceConnectionResult
                {
                    Success = true,
                    Message = "Ticimax API bağlantısı başarılı! API anahtarı geçerli ve servis erişilebilir.",
                    PlatformName = PlatformName,
                    PlatformVersion = "SOAP API",
                    ConnectionInfo = new Dictionary<string, object>
                    {
                        ["platform"] = PlatformName,
                        ["apiType"] = "SOAP",
                    }
                };
            }
            else
            {
                var errorMessage = contents.ErrorMessage ?? "Bilinmeyen hata";

                return new EcommerceConnectionResult
                {
                    Success = false,
                    Message = errorMessage,
                    PlatformName = PlatformName,
                };
            }
        }
        catch (HttpRequestException httpEx)
        {
            _logger.LogError(httpEx, "HTTP error testing Ticimax connection to {ApiUrl}", apiUrl);
            return new EcommerceConnectionResult
            {
                Success = false,
                Message = "Bağlantı hatası: " + httpEx.Message + ". URL'nin doğru olduğundan ve sunucunun erişilebilir olduğundan emin olun.",
                PlatformName = PlatformName,
                ErrorCode = "CONNECTION_ERROR"
            };
        }
        catch (TaskCanceledException tcEx) when (tcEx.InnerException is TimeoutException)
        {
            _logger.LogError(tcEx, "Timeout testing Ticimax connection to {ApiUrl}", apiUrl);
            return new EcommerceConnectionResult
            {
                Success = false,
                Message = "Bağlantı zaman aşımına uğradı. Sunucu yanıt vermiyor olabilir.",
                PlatformName = PlatformName,
                ErrorCode = "TIMEOUT_ERROR"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error testing Ticimax connection to {ApiUrl}", apiUrl);
            return new EcommerceConnectionResult
            {
                Success = false,
                Message = "Beklenmeyen hata: " + ex.Message,
                PlatformName = PlatformName,
                ErrorCode = "UNEXPECTED_ERROR"
            };
        }
    }

    #endregion
}
