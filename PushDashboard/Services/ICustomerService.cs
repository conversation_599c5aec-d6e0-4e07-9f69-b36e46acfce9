using PushDashboard.Models;
using PushDashboard.ViewModels;

namespace PushDashboard.Services;

public interface ICustomerService
{
    Task<CustomerIndexViewModel> GetCustomersAsync(Guid companyId, int page = 1, int pageSize = 50, string? searchTerm = null, string? status = null, string? sortBy = null, string? sortDirection = null);
    Task<(bool Success, string Message)> SyncCustomersAsync(Guid companyId);
    Task<Customer?> GetCustomerByIdAsync(int id, Guid companyId);
    Task<List<SyncLog>> GetSyncLogsAsync(Guid companyId, int limit = 10);
    Task<(int TotalCustomers, int ActiveCustomers, int NewCustomers, DateTime? LastSync)> GetCustomerStatsAsync(Guid companyId);

    // Yeni metodlar
    Task<(bool Success, string Message, int? CustomerId)> CreateCustomerAsync(Guid companyId, CreateCustomerViewModel model);
    Task<(bool Success, string Message, int? JobId)> StartBulkImportAsync(Guid companyId, string userId, IFormFile file);
    Task<CustomerImportProgressViewModel?> GetImportProgressAsync(int jobId, Guid companyId);
    Task<List<CustomerImportJobViewModel>> GetImportHistoryAsync(Guid companyId, int limit = 10);
    byte[] GenerateExcelTemplate();
}
