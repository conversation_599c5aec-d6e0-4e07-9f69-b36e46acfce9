using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Services.Modules.Birthday;
using System.Security.Claims;

namespace PushDashboard.Controllers;

[Authorize]
public class NotificationController : Controller
{
    private readonly ApplicationDbContext _context;
    private readonly IBirthdayModuleService _birthdayModuleService;
    private readonly ILogger<NotificationController> _logger;

    public NotificationController(
        ApplicationDbContext context,
        IBirthdayModuleService birthdayModuleService,
        ILogger<NotificationController> logger)
    {
        _context = context;
        _birthdayModuleService = birthdayModuleService;
        _logger = logger;
    }

    /// <summary>
    /// Belirtilen şirket için doğum günü bildirimleri gönderir
    /// </summary>
    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SendBirthdayNotifications()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var userId = GetCurrentUserId();
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "Kullanıcı bilgisi bulunamadı." });
            }

            var result = await _birthdayModuleService.SendBirthdayNotificationsAsync(companyId.Value, userId);

            return Json(new
            {
                success = result.Success,
                message = result.Message,
                notificationsSent = result.NotificationsSent,
                totalCost = result.TotalCost
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending birthday notifications");
            return Json(new { success = false, message = "Doğum günü bildirimleri gönderilirken hata oluştu: " + ex.Message });
        }
    }

    /// <summary>
    /// Tüm şirketler için doğum günü bildirimleri gönderir (Admin endpoint)
    /// </summary>
    [HttpPost]
    [Authorize(Roles = "Admin")] // Sadece admin kullanıcılar erişebilir
    public async Task<IActionResult> SendBirthdayNotificationsForAllCompanies()
    {
        try
        {
            var result = await _birthdayModuleService.SendBirthdayNotificationsForAllCompaniesAsync();

            return Json(new
            {
                success = result.Success,
                message = result.Message,
                notificationsSent = result.NotificationsSent
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending birthday notifications for all companies");
            return Json(new { success = false, message = "Tüm şirketler için doğum günü bildirimleri gönderilirken hata oluştu: " + ex.Message });
        }
    }

    /// <summary>
    /// Bugün doğum günü olan müşterileri listeler (önizleme için)
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetTodaysBirthdays()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var today = DateTime.Today;
            var birthdayCustomers = await _context.Customers
                .Where(c => c.CompanyId == companyId &&
                           c.IsActive &&
                           c.BirthDate.HasValue &&
                           c.BirthDate.Value.Month == today.Month &&
                           c.BirthDate.Value.Day == today.Day)
                .Select(c => new
                {
                    c.Id,
                    c.FirstName,
                    c.LastName,
                    c.Email,
                    c.BirthDate,
                    c.EmailPermission,
                    Age = today.Year - c.BirthDate.Value.Year
                })
                .ToListAsync();

            return Json(new
            {
                success = true,
                data = birthdayCustomers,
                count = birthdayCustomers.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting today's birthdays");
            return Json(new { success = false, message = "Bugünün doğum günleri alınırken hata oluştu: " + ex.Message });
        }
    }

    /// <summary>
    /// Şirketin aktif iletişim kanallarını listeler
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetActiveNotificationChannels()
    {
        try
        {
            var companyId = GetCurrentUserCompanyId();
            if (companyId == null)
            {
                return Json(new { success = false, message = "Şirket bilgisi bulunamadı." });
            }

            var activeChannels = await _context.CompanyIntegrations
                .Include(ci => ci.Integration)
                .Where(ci => ci.CompanyId == companyId &&
                           ci.IsActive &&
                           ci.IsConfigured &&
                           ci.Integration.Type == "communication")
                .Select(ci => new
                {
                    ci.Integration.Name,
                    ci.Integration.Description,
                    ci.Integration.IconClass,
                    ci.Integration.IconColor,
                    ci.IsActive,
                    ci.IsConfigured,
                    ci.LastSyncAt
                })
                .ToListAsync();

            return Json(new
            {
                success = true,
                data = activeChannels,
                count = activeChannels.Count
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active notification channels");
            return Json(new { success = false, message = "Aktif bildirim kanalları alınırken hata oluştu: " + ex.Message });
        }
    }

    private Guid? GetCurrentUserCompanyId()
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
        if (string.IsNullOrEmpty(userId))
            return null;

        var user = _context.Users
            .Include(u => u.Company)
            .FirstOrDefault(u => u.Id == userId);

        return user?.CompanyId;
    }

    private string? GetCurrentUserId()
    {
        return User.FindFirstValue(ClaimTypes.NameIdentifier);
    }
}
