using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PushDashboard.Data;
using PushDashboard.Models;
using PushDashboard.ViewModels;
using System.Security.Claims;
using System.Text.Json;
using PushDashboard.Services.Integrations;
namespace PushDashboard.Controllers;

public class IntegrationController : BaseController
{
    private readonly ApplicationDbContext _context;
    private readonly IEcommerceWebhookFactory _webhookFactory;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<IntegrationController> _logger;

    public IntegrationController(
        ApplicationDbContext context,
        IEcommerceWebhookFactory webhookFactory,
        IServiceProvider serviceProvider,
        ILogger<IntegrationController> logger)
    {
        _context = context;
        _webhookFactory = webhookFactory;
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);

        // Get user with company
        var user = await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);

        if (user?.Company == null)
        {
            // User doesn't have a company, show empty state
            return View(new IntegrationIndexViewModel());
        }

        // Get all integrations grouped by category
        var integrations = await _context.Integrations
            .Where(i => i.IsActive)
            .OrderBy(i => i.Category)
            .ThenBy(i => i.SortOrder)
            .ToListAsync();

        // Get company's integrations
        var companyIntegrations = await _context.CompanyIntegrations
            .Include(ci => ci.Integration)
            .Where(ci => ci.CompanyId == user.Company.Id)
            .ToListAsync();

        // Group integrations by category
        var groupedIntegrations = integrations
            .GroupBy(i => i.Category)
            .ToDictionary(g => g.Key, g => g.ToList());

        // Create view model
        var viewModel = new IntegrationIndexViewModel
        {
            GroupedIntegrations = groupedIntegrations,
            CompanyIntegrations = companyIntegrations.ToDictionary(ci => ci.IntegrationId, ci => ci)
        };

        return View(viewModel);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AddIntegration([FromBody] AddIntegrationRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcının şirketi bulunamadı." });
            }

            // Check if integration exists
            var integration = await _context.Integrations
                .FirstOrDefaultAsync(i => i.Id == request.IntegrationId && i.IsActive);

            if (integration == null)
            {
                return Json(new { success = false, message = "Entegrasyon bulunamadı." });
            }

            // Check if company already has this integration
            var existingCompanyIntegration = await _context.CompanyIntegrations
                .FirstOrDefaultAsync(ci => ci.CompanyId == user.Company.Id && ci.IntegrationId == request.IntegrationId);

            if (existingCompanyIntegration != null)
            {
                return Json(new { success = false, message = "Bu entegrasyon şirketiniz için zaten eklenmiş." });
            }

            // Create new company integration
            var companyIntegration = new CompanyIntegration
            {
                CompanyId = user.Company.Id,
                IntegrationId = request.IntegrationId,
                IsActive = true,
                IsConfigured = false,
                CreatedAt = DateTime.UtcNow,
                SettingsJson = integration.DefaultSettingsTemplate
            };

            _context.CompanyIntegrations.Add(companyIntegration);
            await _context.SaveChangesAsync();

            return Json(new {
                success = true,
                message = $"{integration.Name} entegrasyonu şirketiniz için başarıyla eklendi. Şimdi ayarlarını yapabilirsiniz."
            });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "Entegrasyon eklenirken bir hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetSettings(int integrationId)
    {
        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcının şirketi bulunamadı." });
            }

            var companyIntegration = await GetCompanyIntegrationAsync(user.Company.Id, integrationId);
            if (companyIntegration == null)
            {
                return Json(new { success = false, message = "Entegrasyon bulunamadı." });
            }

            return Json(new { success = true, settings = companyIntegration.Settings });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "Ayarlar yüklenirken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SaveSettings([FromBody] SaveSettingsRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcının şirketi bulunamadı." });
            }

            var companyIntegration = await GetCompanyIntegrationAsync(user.Company.Id, request.IntegrationId);
            if (companyIntegration == null)
            {
                return Json(new { success = false, message = "Entegrasyon bulunamadı." });
            }

            // Ayarları güncelle
            companyIntegration.Settings = request.Settings;
            companyIntegration.IsConfigured = true;
            companyIntegration.ConfiguredAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            // E-ticaret entegrasyonu ise webhook'ları kontrol et ve ekle
            string webhookMessage = "";
            if (companyIntegration.Integration.Type == "ecommerce")
            {
                var (webhookSuccess, webhookMsg) = await _webhookFactory.EnsureWebhooksForIntegrationAsync(
                    user.Company.Id,
                    companyIntegration.IntegrationId,
                    request.Settings);

                webhookMessage = webhookSuccess ? $" Webhook'lar: {webhookMsg}" : $" Webhook uyarısı: {webhookMsg}";
            }

            return Json(new {
                success = true,
                message = $"{companyIntegration.Integration.Name} ayarları başarıyla kaydedildi.{webhookMessage}"
            });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "Ayarlar kaydedilirken hata oluştu." });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> TestConnection([FromBody] TestConnectionRequest request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage);
            return Json(new { success = false, message = string.Join(" ", errors) });
        }

        try
        {
            // 1. IntegrationId ile Integration'ı bul
            var integration = await _context.Integrations
                .FirstOrDefaultAsync(i => i.Id == request.IntegrationId);

            if (integration == null)
            {
                return Json(new { success = false, message = "Entegrasyon bulunamadı." });
            }

            // 2. Integration tipine göre service'i bul ve test et
            var testResult = await TestConnectionByIntegrationType(integration.Name, request.Settings);

            return Json(new {
                success = testResult.Success,
                message = testResult.Message,
                errorCode = testResult.ErrorCode,
                responseTime = testResult.ResponseTime?.TotalMilliseconds ?? 0,
                additionalInfo = testResult.AdditionalInfo
            });
        }
        catch (Exception ex)
        {
            return Json(new { success = false, message = "Bağlantı test edilirken beklenmeyen bir hata oluştu." });
        }
    }

    private async Task<ConnectionTestResult> TestConnectionByIntegrationType(string integrationType, Dictionary<string, object> settings)
    {
        try
        {
            return integrationType.ToLower() switch
            {
                "ticimax" => await TestTicimaxConnection(settings),
                "email smtp" => await TestSmtpConnection(settings),
                "whatsapp" => await TestWhatsAppConnection(settings),
                // Gelecekte diğer entegrasyonlar buraya eklenecek
                // "shopify" => await TestShopifyConnection(settings),
                // "ikas" => await TestIkasConnection(settings),
                _ => new ConnectionTestResult
                {
                    Success = false,
                    Message = $"'{integrationType}' entegrasyonu için test desteği henüz eklenmemiş.",
                    ErrorCode = "UNSUPPORTED_INTEGRATION"
                }
            };
        }
        catch (Exception ex)
        {
            return new ConnectionTestResult
            {
                Success = false,
                Message = "Bağlantı test edilirken hata oluştu: " + ex.Message,
                ErrorCode = "TEST_ERROR"
            };
        }
    }

    private async Task<ConnectionTestResult> TestTicimaxConnection(Dictionary<string, object> settings)
    {
        try
        {
            // Settings'lerden apiUrl ve apiKey al
            var apiUrl = settings.GetValueOrDefault("apiUrl")?.ToString();
            var apiKey = settings.GetValueOrDefault("apiKey")?.ToString();

            if (string.IsNullOrWhiteSpace(apiUrl))
            {
                return new ConnectionTestResult
                {
                    Success = false,
                    Message = "API URL gereklidir.",
                    ErrorCode = "MISSING_API_URL"
                };
            }

            if (string.IsNullOrWhiteSpace(apiKey))
            {
                return new ConnectionTestResult
                {
                    Success = false,
                    Message = "API Anahtarı gereklidir.",
                    ErrorCode = "MISSING_API_KEY"
                };
            }

            // TicimaxService'i al
            var ticimaxService = _serviceProvider.GetService<PushDashboard.Services.Integrations.Ticimax.TicimaxService>();
            if (ticimaxService == null)
            {
                return new ConnectionTestResult
                {
                    Success = false,
                    Message = "Ticimax servisi bulunamadı.",
                    ErrorCode = "SERVICE_NOT_FOUND"
                };
            }

            // TestConnection metodunu çağır (apiUrl ve apiKey ile)
            var result = await ticimaxService.TestConnectionAsync(apiUrl, apiKey);

            return new ConnectionTestResult
            {
                Success = result.Success,
                Message = result.Message,
                ErrorCode = result.ErrorCode,
                AdditionalInfo = result.ConnectionInfo,
                ResponseTime = TimeSpan.FromMilliseconds(100) // Placeholder
            };
        }
        catch (Exception ex)
        {
            return new ConnectionTestResult
            {
                Success = false,
                Message = "Ticimax bağlantısı test edilirken hata oluştu: " + ex.Message,
                ErrorCode = "TICIMAX_TEST_ERROR"
            };
        }
    }

    private async Task<ConnectionTestResult> TestSmtpConnection(Dictionary<string, object> settings)
    {
        try
        {
            // Get current user's company ID for testing
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return ConnectionTestResult.CreateFailure(
                    "Kullanıcının şirketi bulunamadı.",
                    "COMPANY_NOT_FOUND",
                    "Email SMTP"
                );
            }

            // EmailNotificationChannelService'i al ve test et
            var emailService = _serviceProvider.GetService<PushDashboard.Services.Notifications.EmailNotificationChannelService>();
            if (emailService == null)
            {
                return ConnectionTestResult.CreateFailure(
                    "Email notification servisi bulunamadı.",
                    "SERVICE_NOT_FOUND",
                    "Email SMTP"
                );
            }

            // Test connection using the notification service with provided settings
            var result = await emailService.TestConnectionWithSettingsAsync(user.Company.Id, settings);
            return result;
        }
        catch (Exception ex)
        {
            return ConnectionTestResult.CreateError(ex, "Email SMTP");
        }
    }

    private async Task<ConnectionTestResult> TestWhatsAppConnection(Dictionary<string, object> settings)
    {
        try
        {
            // Get current user's company ID for testing
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return ConnectionTestResult.CreateFailure(
                    "Kullanıcının şirketi bulunamadı.",
                    "COMPANY_NOT_FOUND",
                    "WhatsApp"
                );
            }

            // WhatsAppNotificationChannelService'i al ve test et
            var whatsAppService = _serviceProvider.GetService<PushDashboard.Services.Notifications.WhatsAppNotificationChannelService>();
            if (whatsAppService == null)
            {
                return ConnectionTestResult.CreateFailure(
                    "WhatsApp notification servisi bulunamadı.",
                    "SERVICE_NOT_FOUND",
                    "WhatsApp"
                );
            }

            // Test connection using the notification service with provided settings
            var result = await whatsAppService.TestConnectionWithSettingsAsync(user.Company.Id, settings);
            return result;
        }
        catch (Exception ex)
        {
            return ConnectionTestResult.CreateError(ex, "WhatsApp");
        }
    }

    #region Order Status Module Actions

    [HttpGet]
    public async Task<IActionResult> CheckOrderStatusModule()
    {
        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcının şirketi bulunamadı." });
            }

            // Sipariş Durumu Bildirimleri modülüne sahip mi kontrol et
            var hasModule = await _context.CompanyModules
                .Include(cm => cm.Module)
                .AnyAsync(cm => cm.CompanyId == user.Company.Id &&
                              cm.Module.Name == "Sipariş Durumu Bildirimleri" &&
                              cm.IsActive &&
                              (!cm.ExpiresAt.HasValue || cm.ExpiresAt.Value > DateTime.UtcNow));

            return Json(new { success = true, hasModule = hasModule });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking order status module");
            return Json(new { success = false, message = "Modül kontrolü yapılırken hata oluştu." });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetStatusMappings(int integrationId)
    {
        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcının şirketi bulunamadı." });
            }

            // Entegrasyonu kontrol et
            var companyIntegration = await GetCompanyIntegrationAsync(user.Company.Id, integrationId);
            if (companyIntegration == null)
            {
                return Json(new { success = false, message = "Entegrasyon bulunamadı." });
            }

            // Sadece Ticimax entegrasyonu için
            if (companyIntegration.Integration.Name != "Ticimax")
            {
                return Json(new { success = false, message = "Bu özellik sadece Ticimax entegrasyonu için kullanılabilir." });
            }

            // Ayarlardan durum eşleştirmelerini al
            var statusMappings = new Dictionary<string, string>();
            if (companyIntegration.Settings.ContainsKey("statusMappings"))
            {
                try
                {
                    var mappingsJson = companyIntegration.Settings["statusMappings"].ToString();
                    if (!string.IsNullOrEmpty(mappingsJson))
                    {
                        statusMappings = JsonSerializer.Deserialize<Dictionary<string, string>>(mappingsJson)
                                       ?? new Dictionary<string, string>();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deserializing status mappings for integration {IntegrationId}", integrationId);
                }
            }

            // Eğer hiç eşleştirme yoksa varsayılan eşleştirmeleri ekle
            if (statusMappings.Count == 0)
            {
                statusMappings = GetDefaultTicimaxMappings();
            }

            return Json(new { success = true, data = statusMappings });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting status mappings for integration {IntegrationId}", integrationId);
            return Json(new { success = false, message = "Durum eşleştirmeleri alınırken hata oluştu." });
        }
    }

    [HttpPost]
    public async Task<IActionResult> SaveStatusMappings([FromBody] SaveStatusMappingsRequest request)
    {
        try
        {
            var user = await GetCurrentUserWithCompanyAsync();
            if (user?.Company == null)
            {
                return Json(new { success = false, message = "Kullanıcının şirketi bulunamadı." });
            }

            // Entegrasyonu kontrol et
            var companyIntegration = await GetCompanyIntegrationAsync(user.Company.Id, request.IntegrationId);
            if (companyIntegration == null)
            {
                return Json(new { success = false, message = "Entegrasyon bulunamadı." });
            }

            // Sadece Ticimax entegrasyonu için
            if (companyIntegration.Integration.Name != "Ticimax")
            {
                return Json(new { success = false, message = "Bu özellik sadece Ticimax entegrasyonu için kullanılabilir." });
            }

            // Sipariş Durumu Bildirimleri modülüne sahip mi kontrol et
            var hasModule = await _context.CompanyModules
                .Include(cm => cm.Module)
                .AnyAsync(cm => cm.CompanyId == user.Company.Id &&
                              cm.Module.Name == "Sipariş Durumu Bildirimleri" &&
                              cm.IsActive &&
                              (!cm.ExpiresAt.HasValue || cm.ExpiresAt.Value > DateTime.UtcNow));

            if (!hasModule)
            {
                return Json(new { success = false, message = "Bu özelliği kullanabilmek için Sipariş Durumu Bildirimleri modülüne sahip olmanız gerekiyor." });
            }

            // Ayarlara durum eşleştirmelerini ekle
            var settings = companyIntegration.Settings;
            settings["statusMappings"] = request.StatusMappings;

            companyIntegration.Settings = settings;
            companyIntegration.ConfiguredAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();

            _logger.LogInformation("Status mappings saved for company {CompanyId}, integration {IntegrationId}",
                user.Company.Id, request.IntegrationId);

            return Json(new { success = true, message = "Durum eşleştirmeleri başarıyla kaydedildi." });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving status mappings for integration {IntegrationId}", request.IntegrationId);
            return Json(new { success = false, message = "Durum eşleştirmeleri kaydedilirken hata oluştu." });
        }
    }

    #endregion

    #region Order Status Mapping Helper Methods

    private Dictionary<string, string> GetDefaultTicimaxMappings()
    {
        // Varsayılan eşleştirmeler: Ticimax Kodu -> Bizim 7 Sistem Durumu
        return new Dictionary<string, string>
        {
            // Temel eşleştirmeler
            { "0", "1" },   // Yeni Sipariş -> Beklemede
            { "1", "1" },   // Beklemede -> Beklemede
            { "2", "2" },   // Onaylandı -> Onaylandı
            { "3", "3" },   // Hazırlanıyor -> Hazırlanıyor
            { "4", "4" },   // Kargoya Verildi -> Kargoya Verildi
            { "5", "5" },   // Teslim Edildi -> Teslim Edildi
            { "6", "6" },   // İptal Edildi -> İptal Edildi
            { "7", "7" },   // İade Edildi -> İade Edildi

            // Gelişmiş durumlar
            { "8", "5" },   // Kısmi Teslim -> Teslim Edildi
            { "9", "1" },   // Ödeme Bekliyor -> Beklemede
            { "10", "1" },  // Stok Bekliyor -> Beklemede
            { "11", "1" },  // Tedarikçi Onayı Bekliyor -> Beklemede
            { "12", "3" },  // Kargo Hazırlığı -> Hazırlanıyor
            { "13", "4" },  // Kargoda -> Kargoya Verildi
            { "14", "4" },  // Dağıtımda -> Kargoya Verildi
            { "15", "4" },  // Teslim Edilemedi -> Kargoya Verildi

            // İptal durumları
            { "16", "6" },  // Müşteri İptali -> İptal Edildi
            { "17", "6" },  // Sistem İptali -> İptal Edildi
            { "18", "6" },  // Ödeme İptali -> İptal Edildi
            { "19", "6" },  // Stok İptali -> İptal Edildi
            { "20", "6" },  // Kısmi İptal -> İptal Edildi

            // İade durumları
            { "21", "7" },  // İade Talebi -> İade Edildi
            { "22", "7" },  // İade Onaylandı -> İade Edildi
            { "23", "2" },  // İade Reddedildi -> Onaylandı
            { "24", "7" },  // İade Kargoda -> İade Edildi
            { "25", "7" },  // İade Teslim Alındı -> İade Edildi
            { "26", "7" },  // İade Tamamlandı -> İade Edildi

            // Değişim durumları
            { "27", "3" },  // Değişim Talebi -> Hazırlanıyor
            { "28", "3" },  // Değişim Onaylandı -> Hazırlanıyor
            { "29", "4" },  // Değişim Kargoda -> Kargoya Verildi
            { "30", "5" }   // Değişim Tamamlandı -> Teslim Edildi
        };
    }

    #endregion

    #region Helper Methods

    private async Task<ApplicationUser?> GetCurrentUserWithCompanyAsync()
    {
        var userId = User.FindFirstValue(ClaimTypes.NameIdentifier);
        return await _context.Users
            .Include(u => u.Company)
            .FirstOrDefaultAsync(u => u.Id == userId);
    }

    private async Task<CompanyIntegration?> GetCompanyIntegrationAsync(Guid companyId, int integrationId)
    {
        return await _context.CompanyIntegrations
            .Include(ci => ci.Integration)
            .FirstOrDefaultAsync(ci => ci.CompanyId == companyId && ci.IntegrationId == integrationId);
    }

    #endregion
}