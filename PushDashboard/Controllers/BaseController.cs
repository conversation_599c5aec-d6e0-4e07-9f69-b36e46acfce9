using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using PushDashboard.Models;
using System.Security.Claims;

namespace PushDashboard.Controllers;

[Authorize]
public class BaseController : Controller
{
    protected Guid? GetCurrentUserCompanyId()
    {
        var companyIdClaim = User.FindFirst("CompanyId");
        if (companyIdClaim != null && Guid.TryParse(companyIdClaim.Value, out Guid companyId))
        {
            return companyId;
        }
        return null;
    }

    protected string? GetCurrentUserId()
    {
        return User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
    }
}