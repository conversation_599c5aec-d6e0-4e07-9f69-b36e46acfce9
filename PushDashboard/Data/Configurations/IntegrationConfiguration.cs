using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class IntegrationConfiguration : IEntityTypeConfiguration<Integration>
{
    public void Configure(EntityTypeBuilder<Integration> builder)
    {
        // Configure properties from Data Annotations
        builder.HasKey(i => i.Id);

        builder.Property(i => i.Name)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(i => i.Description)
            .HasMaxLength(500);

        builder.Property(i => i.Category)
            .HasMaxLength(50);

        builder.Property(i => i.LogoUrl)
            .HasMaxLength(100);

        builder.Property(i => i.Features)
            .HasMaxLength(2000);

        builder.Property(i => i.DefaultSettingsTemplate)
            .HasMaxLength(4000);
    }
}
