using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class BasketConfiguration : IEntityTypeConfiguration<Basket>
{
    public void Configure(EntityTypeBuilder<Basket> builder)
    {
        // Configure Basket relationships
        builder
            .HasOne(b => b.Company)
            .WithMany()
            .HasForeignKey(b => b.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure Basket index for ExternalId and CompanyId (performance only, not unique)
        builder
            .HasIndex(b => new { b.ExternalId, b.CompanyId })
            .HasDatabaseName("IX_Baskets_ExternalId_CompanyId_Performance");

        // Performance indexes
        builder
            .HasIndex(b => b.CompanyId)
            .HasDatabaseName("IX_Baskets_CompanyId_Performance");

        builder
            .HasIndex(b => b.BasketDate)
            .HasDatabaseName("IX_Baskets_BasketDate_Performance");
    }
}
