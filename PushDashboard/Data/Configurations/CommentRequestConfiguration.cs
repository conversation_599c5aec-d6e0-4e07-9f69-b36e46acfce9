using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class CommentRequestConfiguration : IEntityTypeConfiguration<CommentRequest>
{
    public void Configure(EntityTypeBuilder<CommentRequest> builder)
    {
        // Configure CommentRequest relationships
        builder
            .HasOne(cr => cr.Company)
            .WithMany()
            .HasForeignKey(cr => cr.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for CommentRequest
        builder
            .HasIndex(cr => cr.CompanyId)
            .HasDatabaseName("IX_CommentRequests_CompanyId_Performance");

        builder
            .HasIndex(cr => cr.CreatedAt)
            .HasDatabaseName("IX_CommentRequests_CreatedAt_Performance");

        builder
            .HasIndex(cr => cr.Status)
            .HasDatabaseName("IX_CommentRequests_Status_Performance");
    }
}
