using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class OrderStatusChangeLogConfiguration : IEntityTypeConfiguration<OrderStatusChangeLog>
{
    public void Configure(EntityTypeBuilder<OrderStatusChangeLog> builder)
    {
        // Configure OrderStatusChangeLog relationships
        builder
            .HasOne(oscl => oscl.Company)
            .WithMany()
            .HasForeignKey(oscl => oscl.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for OrderStatusChangeLog
        builder
            .HasIndex(oscl => oscl.CompanyId)
            .HasDatabaseName("IX_OrderStatusChangeLogs_CompanyId_Performance");

        builder
            .HasIndex(oscl => oscl.OrderId)
            .HasDatabaseName("IX_OrderStatusChangeLogs_OrderId_Performance");

        builder
            .HasIndex(oscl => oscl.StatusChangedAt)
            .HasDatabaseName("IX_OrderStatusChangeLogs_StatusChangedAt_Performance");

        builder
            .HasIndex(oscl => new { oscl.CompanyId, oscl.StatusChangedAt })
            .HasDatabaseName("IX_OrderStatusChangeLogs_Company_Date_Performance");

        builder
            .HasIndex(oscl => new { oscl.CompanyId, oscl.NewStatus, oscl.StatusChangedAt })
            .HasDatabaseName("IX_OrderStatusChangeLogs_Company_Status_Date_Performance");
    }
}
