using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class SyncLogConfiguration : IEntityTypeConfiguration<SyncLog>
{
    public void Configure(EntityTypeBuilder<SyncLog> builder)
    {
        // Configure SyncLog relationships
        builder
            .HasOne(sl => sl.Company)
            .WithMany()
            .HasForeignKey(sl => sl.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
