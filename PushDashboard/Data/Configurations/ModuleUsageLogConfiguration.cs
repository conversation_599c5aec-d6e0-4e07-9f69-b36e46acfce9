using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class ModuleUsageLogConfiguration : IEntityTypeConfiguration<ModuleUsageLog>
{
    public void Configure(EntityTypeBuilder<ModuleUsageLog> builder)
    {
        // Configure ModuleUsageLog relationships
        builder
            .HasOne(mul => mul.Company)
            .WithMany()
            .HasForeignKey(mul => mul.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(mul => mul.Module)
            .WithMany()
            .HasForeignKey(mul => mul.ModuleId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(mul => mul.User)
            .WithMany()
            .HasForeignKey(mul => mul.UserId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for ModuleUsageLog
        builder
            .HasIndex(mul => mul.CompanyId)
            .HasDatabaseName("IX_ModuleUsageLogs_CompanyId_Performance");

        builder
            .HasIndex(mul => mul.CreatedAt)
            .HasDatabaseName("IX_ModuleUsageLogs_CreatedAt_Performance");

        builder
            .HasIndex(mul => new { mul.CompanyId, mul.ModuleId, mul.CreatedAt })
            .HasDatabaseName("IX_ModuleUsageLogs_Company_Module_Date_Performance");
    }
}
