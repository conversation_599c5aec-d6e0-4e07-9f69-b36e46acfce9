using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class UserInvitationConfiguration : IEntityTypeConfiguration<UserInvitation>
{
    public void Configure(EntityTypeBuilder<UserInvitation> builder)
    {
        // Configure UserInvitation relationships
        builder
            .HasOne(ui => ui.Company)
            .WithMany()
            .HasForeignKey(ui => ui.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(ui => ui.CreatedByUser)
            .WithMany()
            .HasForeignKey(ui => ui.CreatedBy)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure UserInvitation unique constraint for Email and CompanyId (prevent duplicate invitations)
        builder
            .HasIndex(ui => new { ui.Email, ui.CompanyId })
            .IsUnique()
            .HasFilter("\"IsUsed\" = false"); // Only enforce uniqueness for unused invitations

        // Configure UserInvitation index for InvitationToken
        builder
            .HasIndex(ui => ui.InvitationToken)
            .IsUnique();
    }
}
