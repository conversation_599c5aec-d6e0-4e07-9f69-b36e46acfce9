using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class CompanyIntegrationConfiguration : IEntityTypeConfiguration<CompanyIntegration>
{
    public void Configure(EntityTypeBuilder<CompanyIntegration> builder)
    {
        // Configure CompanyIntegration relationships
        builder
            .HasOne(ci => ci.Company)
            .WithMany()
            .HasForeignKey(ci => ci.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(ci => ci.Integration)
            .WithMany(i => i.CompanyIntegrations)
            .HasForeignKey(ci => ci.IntegrationId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure CompanyIntegration unique constraint
        builder
            .HasIndex(ci => new { ci.CompanyId, ci.IntegrationId })
            .IsUnique();
    }
}
