using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class BulkMessageConfiguration : IEntityTypeConfiguration<BulkMessage>
{
    public void Configure(EntityTypeBuilder<BulkMessage> builder)
    {
        // Configure BulkMessage relationships
        builder
            .HasOne(bm => bm.Company)
            .WithMany()
            .HasForeignKey(bm => bm.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(bm => bm.User)
            .WithMany()
            .HasForeignKey(bm => bm.UserId)
            .OnDelete(DeleteBehavior.SetNull)
            .IsRequired(false);

        // Performance indexes for BulkMessage
        builder
            .HasIndex(bm => bm.CompanyId)
            .HasDatabaseName("IX_BulkMessages_CompanyId_Performance");

        builder
            .HasIndex(bm => bm.CreatedAt)
            .HasDatabaseName("IX_BulkMessages_CreatedAt_Performance");

        builder
            .HasIndex(bm => bm.Status)
            .HasDatabaseName("IX_BulkMessages_Status_Performance");

        builder
            .HasIndex(bm => new { bm.CompanyId, bm.Status, bm.CreatedAt })
            .HasDatabaseName("IX_BulkMessages_Company_Status_Date_Performance");
    }
}
