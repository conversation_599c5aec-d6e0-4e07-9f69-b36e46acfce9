using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class OrderStatusNotificationConfiguration : IEntityTypeConfiguration<OrderStatusNotification>
{
    public void Configure(EntityTypeBuilder<OrderStatusNotification> builder)
    {
        // Configure OrderStatusNotification relationships
        builder
            .HasOne(osn => osn.Company)
            .WithMany()
            .HasForeignKey(osn => osn.CompanyId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configure OrderStatusNotification unique constraint
        builder
            .HasIndex(osn => new { osn.CompanyId, osn.OrderStatus })
            .IsUnique();

        // Performance indexes for OrderStatusNotification
        builder
            .HasIndex(osn => osn.CompanyId)
            .HasDatabaseName("IX_OrderStatusNotifications_CompanyId_Performance");

        builder
            .HasIndex(osn => osn.IsActive)
            .HasDatabaseName("IX_OrderStatusNotifications_IsActive_Performance");
    }
}
