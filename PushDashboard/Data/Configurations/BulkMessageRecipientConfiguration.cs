using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PushDashboard.Models;

namespace PushDashboard.Data.Configurations;

public class BulkMessageRecipientConfiguration : IEntityTypeConfiguration<BulkMessageRecipient>
{
    public void Configure(EntityTypeBuilder<BulkMessageRecipient> builder)
    {
        // Configure BulkMessageRecipient relationships
        builder
            .HasOne(bmr => bmr.BulkMessage)
            .WithMany(bm => bm.Recipients)
            .HasForeignKey(bmr => bmr.BulkMessageId)
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(bmr => bmr.Customer)
            .WithMany()
            .HasForeignKey(bmr => bmr.CustomerId)
            .OnDelete(DeleteBehavior.Cascade);

        // Performance indexes for BulkMessageRecipient
        builder
            .HasIndex(bmr => bmr.BulkMessageId)
            .HasDatabaseName("IX_BulkMessageRecipients_BulkMessageId_Performance");

        builder
            .HasIndex(bmr => bmr.Status)
            .HasDatabaseName("IX_BulkMessageRecipients_Status_Performance");

        builder
            .HasIndex(bmr => new { bmr.BulkMessageId, bmr.Status })
            .HasDatabaseName("IX_BulkMessageRecipients_BulkMessage_Status_Performance");
    }
}
